import 'package:toii_mesh/gen/assets.gen.dart';
import 'package:toii_mesh/generated/l10n.dart';
 

final List<OnboardingModel> listOnboarding = [
  OnboardingModel(
    image: Assets.images.image1,
    message: S.current.onboarding_welcome,
    message1: "<PERSON><PERSON><PERSON>",
    description: "",
  ),
  OnboardingModel(
    image: Assets.images.image2,
    message: "",
    message1: "",
    description:"",
  ),
  OnboardingModel(
    image: Assets.images.image3,
    message: "",
    message1: "",
    description: "",
  ),
];

class OnboardingModel {
  final AssetGenImage image;
  final String message;
  final String message1;
  final String description;
  OnboardingModel({
    required this.image,
    required this.message,
    required this.message1,
    required this.description,
  });
}
