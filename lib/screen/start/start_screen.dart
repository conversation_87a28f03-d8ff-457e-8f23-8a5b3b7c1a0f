import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/cubit/auth/login/login/login_cubit.dart';
import 'package:toii_mesh/cubit/theme/theme_cubit.dart';
import 'package:toii_mesh/gen/assets.gen.dart';
import 'package:toii_mesh/model/user/user_model.dart';
import 'package:toii_mesh/router/app_router.dart';
import 'package:toii_mesh/utils/keychain/keychain_service.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';
import 'package:toii_mesh/widget/images/avatar_widget.dart';
import 'package:toii_mesh/widget/snack_bar/snackbar.dart';
import 'package:web3dart/crypto.dart';
import 'package:web3dart/web3dart.dart';

class StartScreen extends StatefulWidget {
  const StartScreen({super.key});

  @override
  State<StartScreen> createState() => _StartScreenState();
}

class _StartScreenState extends State<StartScreen> {
  Map<String, UserModel> profileData = {};
  @override
  void initState() {
    super.initState();
    Future.microtask(() async {
      getData();
    });
  }

  void getData() async {
    try {
      final profile = await KeychainService.instance.getProfile();
      final map = profile.map(
        (key, value) => MapEntry(key, UserModel.fromJson(value)),
      );
      setState(() {
        profileData = map;
      });
    } catch (e) {
      print(e);
    }
  }

  Future<void> _loginWithWallet(BuildContext context, String walletKey) async {
    try {
      EasyLoading.show();

      // 1. Lấy private key từ keychain
      final privateKey =
          await KeychainService.instance.readPrivateKeyFromiCloud(walletKey);
      if (privateKey == null || privateKey.isEmpty) {
        EasyLoading.dismiss();
        context.showSnackbar(message: "Private key not found for this wallet");
        return;
      }

      // 2. Lấy nonce message từ server
      final loginCubit = context.read<LoginCubit>();
      final nonceMessage =
          await loginCubit.getNonceMessageForWalletSignature(walletKey);
      if (nonceMessage == null) {
        EasyLoading.dismiss();
        context.showSnackbar(message: "Failed to get nonce message");
        return;
      }

      // 3. Tạo signature bằng cách ký message với private key
      final signature = await _personalSignByPrivateKey(
        privateKeyHex: privateKey,
        message: nonceMessage,
      );

      EasyLoading.dismiss();

      // 4. Thực hiện login với signature đã tạo
      loginCubit.loginWithWallet(
        address: walletKey,
        signature: signature,
      );
    } catch (e) {
      EasyLoading.dismiss();
      context.showSnackbar(message: "Error: ${e.toString()}");
    }
  }

  Future<String> _personalSignByPrivateKey({
    required String privateKeyHex,
    required String message,
  }) async {
    final key = EthPrivateKey.fromHex(privateKeyHex);
    final msgBytes = Uint8List.fromList(utf8.encode(message));

    // web3dart applies the EIP-191 prefix internally for "personal" signing
    final sigBytes = key.signPersonalMessageToUint8List(msgBytes);

    return bytesToHex(sigBytes, include0x: true);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit(),
      child: BlocConsumer<LoginCubit, LoginState>(
        listener: (context, state) {
          if (state.status.isSuccess) {
            context.go(RouterEnums.inital.routeName);
          }
          if (state.status.isFailure) {
            context.showSnackbar(message: state.message ?? "");
          }
        },
        builder: (context, state) {
          final profileKeys = profileData.keys.toList();
          return Scaffold(
            backgroundColor: Colors.transparent,
            extendBodyBehindAppBar: true,
            body: Container(
              height: double.infinity,
              width: double.infinity,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: Assets.images.bgHome.provider(),
                  fit: BoxFit.cover,
                ),
              ),
              child: SafeArea(
                bottom: false,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    children: [
                      // Logo at top left
                      Align(
                        alignment: Alignment.topLeft,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 24, left: 0),
                          child: _buildLogo(),
                        ),
                      ),

                      // Main content area
                      Expanded(
                        child: profileKeys.isNotEmpty
                            ? _buildSingleProfile(profileKeys.first)
                            : _buildEmptyState(),
                      ),

                      // Bottom buttons
                      _nextButton(),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLogo() {
    return Assets.icons.icBrand.image();
  }

  Widget _buildSingleProfile(String walletKey) {
    return Center(
      child: GestureDetector(
        onTap: () async {
          await _loginWithWallet(context, walletKey);
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AvatarWidget(
              size: 154,
              imageUrl: profileData[walletKey]?.avatarUrl,
              name: profileData[walletKey]?.username ?? '',
            ),
            const SizedBox(height: 17),
            Text(
              profileData[walletKey]?.username ?? "Displayname",
              style: titleMedium.copyColor(themeData.neutral800),
            ),
            const SizedBox(height: 8),
            Text(
              "@${(profileData[walletKey]?.username ?? "username").toLowerCase()}",
              style: const TextStyle(
                fontFamily: 'Manrope',
                fontWeight: FontWeight.w500,
                fontSize: 12,
                height: 1.33,
                letterSpacing: 0.5,
                color: Color(0xFF777777),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_outline,
            size: 154,
            color: Color(0xFF777777),
          ),
          SizedBox(height: 17),
          Text(
            "No Profile",
            style: TextStyle(
              fontFamily: 'IBM Plex Sans',
              fontWeight: FontWeight.w600,
              fontSize: 16,
              height: 1.5,
              letterSpacing: 0.5,
              color: Color(0xFF292929),
            ),
          ),
        ],
      ),
    );
  }

  Widget _nextButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          padding: EdgeInsets.only(
            left: 16,
            right: 16,
            bottom: MediaQuery.of(context).padding.bottom + 16,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // JOIN MESHII button (primary)
              Expanded(
                flex: 2,
                child: GestureDetector(
                  onTap: () {
                    context.push(RouterEnums.createAcount.routeName);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 56,
                    decoration: BoxDecoration(
                      color: const Color(0xFF6C4EFF), // Primary blue
                      borderRadius: BorderRadius.circular(48),
                    ),
                    child: const Text(
                      'JOIN MESHII',
                      style: TextStyle(
                        fontFamily: 'IBM Plex Sans',
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        height: 1.5,
                        letterSpacing: 0.5,
                        color: Color(0xE6FFFFFF), // White with 90% opacity
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 2),
              // RESTORE button (secondary)
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    // Handle restore action
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 54,
                    decoration: BoxDecoration(
                      color: const Color(0xCC000000), // Black with 80% opacity
                      borderRadius: BorderRadius.circular(48),
                    ),
                    child: const Text(
                      'RESTORE',
                      style: TextStyle(
                        fontFamily: 'IBM Plex Sans',
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        height: 1.5,
                        letterSpacing: 0.5,
                        color: Color(0xE6FFFFFF), // White with 90% opacity
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
